[{"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -mlongcalls  -fdiagnostics-color=always -o CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32s3.c.obj -c D:\\vscode\\projects-lvgl\\i2c_tools\\build\\bootloader\\project_elf_src_esp32s3.c", "file": "D:\\vscode\\projects-lvgl\\i2c_tools\\build\\bootloader\\project_elf_src_esp32s3.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\eri.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\xtensa\\eri.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\xtensa\\eri.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\xt_trax.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\xtensa\\xt_trax.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\xtensa\\xt_trax.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\lldesc.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\lldesc.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\dport_access_common.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\dport_access_common.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\interrupts.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\interrupts.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\interrupts.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\gpio_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\gpio_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\gpio_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\uart_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\uart_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\uart_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\adc_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\adc_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\adc_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\dedic_gpio_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\dedic_gpio_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\dedic_gpio_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\gdma_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\gdma_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\gdma_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\spi_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\spi_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\spi_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\ledc_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\ledc_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\ledc_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\pcnt_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\pcnt_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\pcnt_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\rmt_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\rmt_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\rmt_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\sdm_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\sdm_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\sdm_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\i2s_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\i2s_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\i2s_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\i2c_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\i2c_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\i2c_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\temperature_sensor_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\temperature_sensor_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\temperature_sensor_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\timer_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\timer_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\timer_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\lcd_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\lcd_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\lcd_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\mcpwm_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\mcpwm_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\mcpwm_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\sdmmc_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\sdmmc_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\sdmmc_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\touch_sensor_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\touch_sensor_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\touch_sensor_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\twai_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\twai_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\twai_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\usb_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\usb_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\usb_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\usb_otg_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\usb_otg_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\usb_otg_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32s3\\rtc_io_periph.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\rtc_io_periph.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\soc\\esp32s3\\rtc_io_periph.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mpu_hal.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\mpu_hal.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\mpu_hal.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\efuse_hal.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\efuse_hal.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32s3\\efuse_hal.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\esp32s3\\efuse_hal.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\esp32s3\\efuse_hal.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\mmu_hal.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\mmu_hal.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cache_hal.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\cache_hal.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\hal\\cache_hal.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include/spi_flash -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\spi_flash\\spi_flash_wrap.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\spi_flash\\spi_flash_wrap.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -D PROJECT_NAME=\\\"bootloader\\\" -DPROJECT_VER=\\\"v5.1.2\\\" -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_app_format\\CMakeFiles\\__idf_esp_app_format.dir\\esp_app_desc.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_app_format\\esp_app_desc.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_app_format\\esp_app_desc.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_common.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_common.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_common_loader.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_common_loader.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_init.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_init.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_mem.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_mem.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_random.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_random.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32s3.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_random_esp32s3.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_random_esp32s3.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_efuse.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_efuse.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\flash_encrypt.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\flash_encrypt.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\secure_boot.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\secure_boot.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32s3.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_utility.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_utility.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\flash_partitions.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\flash_partitions.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp_image_format.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp_image_format.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_init.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_init.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_loader.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_console.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_console.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_console_loader.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_console_loader.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_sha.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_sha.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_sha.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_soc.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_soc.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_soc.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32s3\\bootloader_esp32s3.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_esp32s3.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\esp32s3\\bootloader_esp32s3.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_panic.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader_support\\src\\bootloader_panic.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_table.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_table.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_table.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_fields.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_fields.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_fields.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_rtc_calib.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_rtc_calib.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_rtc_calib.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32s3\\esp_efuse_utility.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_utility.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\esp32s3\\esp_efuse_utility.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\esp_efuse_api.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\esp_efuse_api.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\esp_efuse_fields.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\esp_efuse_fields.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\esp_efuse_utility.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\esp_efuse_utility.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/private_include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/esp_app_format/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_system\\esp_err.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_system\\esp_err.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\cpu.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\cpu.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\esp_memory_utils.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\esp_memory_utils.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\cpu_region_protect.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\cpu_region_protect.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\cpu_region_protect.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_clk.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_clk_init.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk_init.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_clk_init.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_init.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_init.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_init.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_sleep.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_sleep.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_sleep.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\rtc_time.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_time.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\rtc_time.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32s3\\chip_info.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\chip_info.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_hw_support\\port\\esp32s3\\chip_info.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/efuse/include -ID:/esp/v5.1.2/esp-idf/components/efuse/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -ID:/esp/v5.1.2/esp-idf/components/spi_flash/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_common\\src\\esp_err_to_name.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_common\\src\\esp_err_to_name.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_crc.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_crc.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_sys.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_sys.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_uart.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_uart.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_spiflash.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_efuse.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_efuse.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_longjmp.S.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_longjmp.S", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_longjmp.S"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_systimer.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_systimer.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_systimer.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_wdt.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_wdt.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_wdt.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_cache_esp32s2_esp32s3.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_esp32s2_esp32s3.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_esp32s2_esp32s3.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_cache_writeback_esp32s3.S.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_writeback_esp32s3.S", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\esp_rom\\patches\\esp_rom_cache_writeback_esp32s3.S"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\log\\log.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\log\\log.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_buffers.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\log\\log_buffers.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\log\\log_buffers.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/hal/include -ID:/esp/v5.1.2/esp-idf/components/hal/platform_port/include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_noos.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\log\\log_noos.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\log\\log_noos.c"}, {"directory": "D:/vscode/projects-lvgl/i2c_tools/build/bootloader", "command": "D:\\esp\\Espressif\\tools\\xtensa-esp32s3-elf\\esp-12.2.0_20230208\\xtensa-esp32s3-elf\\bin\\xtensa-esp32s3-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.1.2\\\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -ID:/vscode/projects-lvgl/i2c_tools/build/bootloader/config -ID:/esp/v5.1.2/esp-idf/components/log/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include -ID:/esp/v5.1.2/esp-idf/components/esp_rom/include/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_rom/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_common/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/include/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/. -ID:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32s3/private_include -ID:/esp/v5.1.2/esp-idf/components/newlib/platform_include -ID:/esp/v5.1.2/esp-idf/components/xtensa/include -ID:/esp/v5.1.2/esp-idf/components/xtensa/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/soc/include -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3 -ID:/esp/v5.1.2/esp-idf/components/soc/esp32s3/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/v5.1.2/esp-idf/components/bootloader_support/private_include -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/v5.1.2/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj -c D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader\\subproject\\main\\bootloader_start.c", "file": "D:\\esp\\v5.1.2\\esp-idf\\components\\bootloader\\subproject\\main\\bootloader_start.c"}]