# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.5)

file(MAKE_DIRECTORY
  "D:/esp/v5.1.2/esp-idf/components/bootloader/subproject"
  "D:/vscode/projects-lvgl/i2c_tools/build/bootloader"
  "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix"
  "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix/tmp"
  "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix/src/bootloader-stamp"
  "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix/src"
  "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix/src/bootloader-stamp"
)

set(configSubDirs )
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix/src/bootloader-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "D:/vscode/projects-lvgl/i2c_tools/build/bootloader-prefix/src/bootloader-stamp${cfgdir}") # cfgdir has leading slash
endif()
