/* cmd_i2ctools.h

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

void register_i2ctools(void);

// SC16IS752 I2C to Serial converter functions
void register_sc16is752(void);

#ifdef __cplusplus
}
#endif
