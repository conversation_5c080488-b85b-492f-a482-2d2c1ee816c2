| Supported Targets | ESP32 | ESP32-C2 | ESP32-C3 | ESP32-C6 | ESP32-H2 | ESP32-S2 | ESP32-S3 |
| ----------------- | ----- | -------- | -------- | -------- | -------- | -------- | -------- |

# I2C Tools Example with SC16IS752 Support

(See the README.md file in the upper level 'examples' directory for more information about examples.)

## Overview

This example demonstrates usage of I2C tools from console, including support for the SC16IS752 I2C to Serial converter.

### Standard I2C Tools
[I2C Tools](https://i2c.wiki.kernel.org/index.php/I2C_Tools) is a simple but very useful tool for developing I2C related applications, which is also famous in Linux platform. This example implements basic features of [I2C Tools](https://i2c.wiki.kernel.org/index.php/I2C_Tools) based on [esp32 console component](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/system/console.html):

1. `i2cconfig`: Configure the I2C bus with specific GPIO number, port number and frequency.
2. `i2cdetect`: Scan an I2C bus for devices and output a table with the list of detected devices on the bus.
3. `i2cget`: Read registers visible through the I2C bus.
4. `i2cset`: Set registers visible through the I2C bus.
5. `i2cdump`: Examine registers visible through the I2C bus.

### SC16IS752 I2C to Serial Converter Support
This example also includes specialized commands for the SC16IS752 dual UART with I2C interface:

6. `sc16is752init`: Initialize the SC16IS752 for 115200 baud serial communication
7. `sc16is752send`: Send hex string data through the serial port
8. `sc16is752receive`: Receive data from the serial port and display as hex

The SC16IS752 functionality allows you to use I2C to control a serial interface, enabling communication with external serial devices at 115200 baud through hex string commands.

## How to use example

### Hardware Required

#### Basic I2C Tools
To run the basic I2C tools, you should have any ESP32, ESP32-S and ESP32-C based development board. For test purpose, you should have a device with I2C interface as well.

#### SC16IS752 Setup
For SC16IS752 functionality, you need:
- ESP32 development board
- CJMCU-752 dual SC16IS752 module or equivalent
- External serial device for testing
- Proper I2C connections

#### Pin Assignment:

**Note:** The following pin assignments are used by default, you can change them with `i2cconfig` command at any time.

|                     | SDA    | SCL    | GND  | Other | VCC  |
| ------------------- | ------ | ------ | ---- | ----- | ---- |
| ESP32 I2C Master    | GPIO18 | GPIO19 | GND  | GND   | 3.3V |
| ESP32-S2 I2C Master | GPIO18 | GPIO19 | GND  | GND   | 3.3V |
| ESP32-S3 I2C Master | GPIO8  | GPIO9  | GND  | GND   | 3.3V |
| ESP32-C3 I2C Master | GPIO5  | GPIO6  | GND  | GND   | 3.3V |
| ESP32-C2 I2C Master | GPIO5  | GPIO6  | GND  | GND   | 3.3V |
| ESP32-H2 I2C Master | GPIO8  | GPIO9  | GND  | GND   | 3.3V |
| SC16IS752 Module    | SDA    | SCL    | GND  | A1/A0 | VCC  |

#### SC16IS752 Wiring
Connect the SC16IS752 module to your ESP32:
- **SDA**: Connect to ESP32 SDA pin (see table above)
- **SCL**: Connect to ESP32 SCL pin (see table above)
- **VCC**: Connect to 3.3V
- **GND**: Connect to GND
- **A1/A0**: Set I2C address (default configuration assumes address 0x4D)

#### Serial Device Connection
Connect your external serial device to the SC16IS752:
- **TXA**: Connect to RX of external device
- **RXA**: Connect to TX of external device
- **GND**: Common ground

**Note:** It is recommended to add external pull-up resistors for SDA/SCL pins to make the communication more stable, though the driver will enable internal pull-up resistors.

### Configure the project

Open the project configuration menu (`idf.py menuconfig`). Then go into `Example Configuration` menu.

- You can choose whether or not to save command history into flash in `Store command history in flash` option.

### Build and Flash

Run `idf.py -p PORT flash monitor` to build and flash the project..

(To exit the serial monitor, type ``Ctrl-]``.)

See the [Getting Started Guide](https://docs.espressif.com/projects/esp-idf/en/latest/get-started/index.html) for full steps to configure and use ESP-IDF to build projects.

## Example Usage

### Basic I2C Operations

1. **Configure I2C bus:**
   ```
   i2c-tools> i2cconfig --sda 18 --scl 19
   ```

2. **Scan for devices:**
   ```
   i2c-tools> i2cdetect
        0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f
   00: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
   10: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
   20: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
   30: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
   40: -- -- -- -- -- -- -- -- -- -- -- -- -- 4d -- --
   50: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
   60: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
   70: -- -- -- -- -- -- -- --
   ```

### SC16IS752 Operations

1. **Initialize SC16IS752:**
   ```
   i2c-tools> sc16is752init
   Initializing SC16IS752 at I2C address 0x4D
   SC16IS752 initialized successfully for 115200 baud
   ```

   Or specify a different I2C address:
   ```
   i2c-tools> sc16is752init -a 0x48
   ```

2. **Send hex data:**
   ```
   i2c-tools> sc16is752send DEADBEEF
   Sending hex string: DEADBEEF
   Sent: 0xDE
   Sent: 0xAD
   Sent: 0xBE
   Sent: 0xEF
   Hex string sent successfully
   ```

3. **Receive data:**
   ```
   i2c-tools> sc16is752receive
   Waiting for data (timeout: 5000 ms, max bytes: 64)...
   Received: 0x41
   Received: 0x42
   Received: 0x43

   Received 3 bytes as hex string: 414243
   ```

   With custom timeout and max bytes:
   ```
   i2c-tools> sc16is752receive -t 10000 -m 32
   ```

## SC16IS752 Configuration Details

### I2C Address Configuration
The SC16IS752 I2C address is determined by the A1 and A0 pins:
- A1=0, A0=0: Address 0x48
- A1=0, A0=1: Address 0x49
- A1=1, A0=0: Address 0x4C
- A1=1, A0=1: Address 0x4D (default)

### UART Configuration
The SC16IS752 is automatically configured for:
- **Baud Rate**: 115200
- **Data Bits**: 8
- **Parity**: None
- **Stop Bits**: 1
- **Flow Control**: None

### Crystal Frequency
The code assumes a 14.7456 MHz crystal oscillator. If your module uses a different frequency, you may need to adjust the baud rate divisor values in the code.

## Example Output

### Check all supported commands and their usages

```bash
 ==============================================================
 |             Steps to Use i2c-tools                         |
 |                                                            |
 |  1. Try 'help', check all supported commands               |
 |  2. Try 'i2cconfig' to configure your I2C bus              |
 |  3. Try 'i2cdetect' to scan devices on the bus             |
 |  4. Try 'i2cget' to get the content of specific register   |
 |  5. Try 'i2cset' to set the value of specific register     |
 |  6. Try 'i2cdump' to dump all the register (Experiment)    |
 |                                                            |
 |             SC16IS752 I2C to Serial Commands:              |
 |  7. Try 'sc16is752init' to initialize the converter        |
 |  8. Try 'sc16is752send <hex>' to send hex data             |
 |  9. Try 'sc16is752receive' to receive hex data             |
 |                                                            |
 ==============================================================

i2c-tools> help
help
  Print the list of registered commands

i2cconfig  [--port=<0|1>] [--freq=<Hz>] --sda=<gpio> --scl=<gpio>
  Config I2C bus

i2cdetect
  Scan I2C bus for devices

i2cget  -c <chip_addr> [-r <register_addr>] [-l <length>]
  Read registers visible through the I2C bus

i2cset  -c <chip_addr> [-r <register_addr>] [<data>]...
  Set registers visible through the I2C bus

i2cdump  -c <chip_addr> [-s <size>]
  Examine registers visible through the I2C bus

sc16is752init  [-a <address>]
  Initialize SC16IS752 I2C to Serial converter for 115200 baud

sc16is752send  <hex_string>
  Send hex string through SC16IS752 serial port

sc16is752receive  [-t <ms>] [-m <bytes>]
  Receive data from SC16IS752 serial port and display as hex
```

### Example SC16IS752 Session

```bash
i2c-tools> i2cconfig --sda 18 --scl 19
i2c-tools> i2cdetect
     0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f
00: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
10: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
20: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
30: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
40: -- -- -- -- -- -- -- -- -- -- -- -- -- 4d -- --
50: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
60: -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
70: -- -- -- -- -- -- -- --
i2c-tools> sc16is752init
Initializing SC16IS752 at I2C address 0x4D
SC16IS752 initialized successfully for 115200 baud
i2c-tools> sc16is752send 48656C6C6F
Sending hex string: 48656C6C6F
Sent: 0x48
Sent: 0x65
Sent: 0x6C
Sent: 0x6C
Sent: 0x6F
Hex string sent successfully
i2c-tools> sc16is752receive
Waiting for data (timeout: 5000 ms, max bytes: 64)...
Received: 0x4F
Received: 0x4B
Received: 0x0D
Received: 0x0A

Received 4 bytes as hex string: 4F4B0D0A
i2c-tools>
```

## Troubleshooting

* I don’t find any available address when running `i2cdetect` command.
  * Make sure your wiring connection is right.
  * Some sensor will have a “wake up” pin, via which user can put the sensor into a sleep mode. So make sure your sensor in **not** in the sleep state.
  * Reset you I2C device, and then run `i2cdetect` again.
* I can’t get the right content when running `i2cdump` command.
  * Currently the `i2cdump` only support those who have the same content length of registers inside the I2C device. For example, if a device have three register addresses, and the content length at these address are 1 byte, 2 bytes and 4 bytes. In this case you should not expect this command to dump the register correctly.


### Common SC16IS752 Issues

1. **SC16IS752 not detected:**
   - Check I2C wiring (SDA, SCL, VCC, GND)
   - Verify I2C address configuration (A1/A0 pins)
   - Use `i2cdetect` to scan for the device

2. **No data received:**
   - Ensure external serial device is connected and powered
   - Check baud rate compatibility (115200)
   - Verify TX/RX connections are not swapped

3. **Initialization fails:**
   - Check crystal oscillator frequency (should be 14.7456 MHz)
   - Verify power supply stability
   - Ensure proper I2C pull-up resistors

4. **Wrong I2C address:**
   - The user mentioned address 46 (0x2E), but typical SC16IS752 addresses are 0x48-0x57
   - Check the actual address using `i2cdetect`
   - Use the `-a` option with `sc16is752init` to specify the correct address

### Address Clarification
If your SC16IS752 is at address 46 (0x2E), use:
```
i2c-tools> sc16is752init -a 0x2E
```

(For any technical queries, please open an [issue](https://github.com/espressif/esp-idf/issues) on GitHub. We will get back to you as soon as possible.)

