# SC16IS752 Usage Examples

This document provides practical examples for using the SC16IS752 I2C to Serial converter with the enhanced i2c-tools.

## Quick Start Guide

### 1. Hardware Setup

1. Connect your CJMCU-752 (SC16IS752) module to ESP32:
   - VCC → 3.3V
   - GND → GND
   - SDA → GPIO 18 (ESP32) or GPIO 8 (ESP32-S3)
   - SCL → GPIO 19 (ESP32) or GPIO 9 (ESP32-S3)

2. Connect your external serial device to SC16IS752:
   - TXA → RX of external device
   - RXA → TX of external device
   - GND → Common ground

### 2. Basic Configuration

```bash
# Configure I2C bus
i2c-tools> i2cconfig --sda 18 --scl 19

# Scan for devices (should show SC16IS752 at 0x4D or your configured address)
i2c-tools> i2cdetect

# Initialize SC16IS752 for 115200 baud
i2c-tools> sc16is752init
```

### 3. Sending Data

#### Send ASCII Text as Hex
To send "Hello" (ASCII: 48 65 6C 6C 6F):
```bash
i2c-tools> sc16is752send 48656C6C6F
```

#### Send AT Commands
To send "AT\r\n" (common for modems):
```bash
i2c-tools> sc16is752send 41540D0A
```

#### Send Binary Data
To send bytes 0xDE, 0xAD, 0xBE, 0xEF:
```bash
i2c-tools> sc16is752send DEADBEEF
```

### 4. Receiving Data

#### Basic Receive (5 second timeout)
```bash
i2c-tools> sc16is752receive
```

#### Extended Receive (10 second timeout, max 128 bytes)
```bash
i2c-tools> sc16is752receive -t 10000 -m 128
```

## Common Use Cases

### 1. Testing AT Command Modems

```bash
# Initialize
i2c-tools> sc16is752init

# Send AT command
i2c-tools> sc16is752send 41540D0A

# Wait for response
i2c-tools> sc16is752receive
# Expected response: 4F4B0D0A (OK\r\n)

# Query signal strength
i2c-tools> sc16is752send 41542B4353510D0A  # AT+CSQ\r\n
i2c-tools> sc16is752receive
```

### 2. Communicating with GPS Modules

```bash
# Initialize
i2c-tools> sc16is752init

# Send NMEA command to get GPS info
i2c-tools> sc16is752send 244750474741240D0A  # $GPGGA$\r\n

# Receive GPS data
i2c-tools> sc16is752receive -t 2000
```

### 3. Testing Serial Sensors

```bash
# Initialize
i2c-tools> sc16is752init

# Send sensor query command (example)
i2c-tools> sc16is752send 010300000002C40B  # Modbus RTU read command

# Receive sensor response
i2c-tools> sc16is752receive -t 1000
```

## Hex String Conversion Reference

### ASCII to Hex Conversion
| Character | Hex | Character | Hex | Character | Hex | Character | Hex |
|-----------|-----|-----------|-----|-----------|-----|-----------|-----|
| A         | 41  | a         | 61  | 0         | 30  | Space     | 20  |
| B         | 42  | b         | 62  | 1         | 31  | !         | 21  |
| C         | 43  | c         | 63  | 2         | 32  | "         | 22  |
| D         | 44  | d         | 64  | 3         | 33  | #         | 23  |
| E         | 45  | e         | 65  | 4         | 34  | $         | 24  |
| F         | 46  | f         | 66  | 5         | 35  | %         | 25  |
| G         | 47  | g         | 67  | 6         | 36  | &         | 26  |
| H         | 48  | h         | 68  | 7         | 37  | '         | 27  |
| I         | 49  | i         | 69  | 8         | 38  | (         | 28  |
| J         | 4A  | j         | 6A  | 9         | 39  | )         | 29  |

### Control Characters
| Character | Hex | Description |
|-----------|-----|-------------|
| \r        | 0D  | Carriage Return |
| \n        | 0A  | Line Feed |
| \t        | 09  | Tab |
| NULL      | 00  | Null character |

## Troubleshooting Tips

### Address Issues
If your SC16IS752 is at a different address:
```bash
# Check actual address
i2c-tools> i2cdetect

# Initialize with specific address (example: 0x48)
i2c-tools> sc16is752init -a 0x48
```

### No Response from External Device
1. Check baud rate compatibility (SC16IS752 is set to 115200)
2. Verify TX/RX connections are not swapped
3. Ensure external device is powered and ready
4. Check if external device requires specific initialization sequence

### Data Corruption
1. Verify I2C connections and pull-up resistors
2. Check power supply stability
3. Reduce I2C frequency if needed:
   ```bash
   i2c-tools> i2cconfig --sda 18 --scl 19 --freq 50000
   ```

### Timeout Issues
1. Increase timeout for slow devices:
   ```bash
   i2c-tools> sc16is752receive -t 15000
   ```
2. Check if external device is responding at all
3. Verify external device is configured for 115200 baud

## Advanced Usage

### Custom I2C Address Configuration
The SC16IS752 address depends on A1/A0 pin configuration:
- A1=0, A0=0: 0x48
- A1=0, A0=1: 0x49  
- A1=1, A0=0: 0x4C
- A1=1, A0=1: 0x4D (default)

### Batch Operations
You can create scripts for repetitive operations:
```bash
# Example: Continuous GPS monitoring
i2c-tools> sc16is752init
i2c-tools> sc16is752send 244750474741240D0A
i2c-tools> sc16is752receive -t 2000
# Repeat send/receive as needed
```

## Integration with External Tools

The hex output format makes it easy to integrate with external tools:
- Copy hex strings to hex editors
- Parse responses in scripts
- Log communication for debugging
- Convert to other formats as needed

For more complex automation, consider writing custom scripts that parse the console output and automate the command sequences.
