/*
 * SPDX-FileCopyrightText: 2022-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */
/* cmd_i2ctools.c

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/
#include <stdio.h>
#include <string.h>
#include <ctype.h>
#include "argtable3/argtable3.h"
#include "driver/i2c.h"
#include "esp_console.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#define I2C_MASTER_TX_BUF_DISABLE 0 /*!< I2C master doesn't need buffer */
#define I2C_MASTER_RX_BUF_DISABLE 0 /*!< I2C master doesn't need buffer */
#define WRITE_BIT I2C_MASTER_WRITE  /*!< I2C master write */
#define READ_BIT I2C_MASTER_READ    /*!< I2C master read */
#define ACK_CHECK_EN 0x1            /*!< I2C master will check ack from slave*/
#define ACK_CHECK_DIS 0x0           /*!< I2C master will not check ack from slave */
#define ACK_VAL 0x0                 /*!< I2C ack value */
#define NACK_VAL 0x1                /*!< I2C nack value */

static const char *TAG = "cmd_i2ctools";

// SC16IS752 I2C to Serial converter definitions
#define SC16IS752_DEFAULT_ADDR    0x4D  // Default I2C address (can be 0x48-0x57 depending on A1/A0 pins)
#define SC16IS752_CHANNEL_A       0x00  // Channel A register offset
#define SC16IS752_CHANNEL_B       0x01  // Channel B register offset

// SC16IS752 Register definitions (shifted left by 3 bits as per datasheet)
#define SC16IS752_RHR_REG         (0x00 << 3)  // Receive Holding Register
#define SC16IS752_THR_REG         (0x00 << 3)  // Transmit Holding Register
#define SC16IS752_IER_REG         (0x01 << 3)  // Interrupt Enable Register
#define SC16IS752_FCR_REG         (0x02 << 3)  // FIFO Control Register
#define SC16IS752_IIR_REG         (0x02 << 3)  // Interrupt Identification Register
#define SC16IS752_LCR_REG         (0x03 << 3)  // Line Control Register
#define SC16IS752_MCR_REG         (0x04 << 3)  // Modem Control Register
#define SC16IS752_LSR_REG         (0x05 << 3)  // Line Status Register
#define SC16IS752_MSR_REG         (0x06 << 3)  // Modem Status Register
#define SC16IS752_SPR_REG         (0x07 << 3)  // Scratchpad Register
#define SC16IS752_TCR_REG         (0x06 << 3)  // Transmission Control Register
#define SC16IS752_TLR_REG         (0x07 << 3)  // Trigger Level Register
#define SC16IS752_TXLVL_REG       (0x08 << 3)  // Transmit FIFO Level Register
#define SC16IS752_RXLVL_REG       (0x09 << 3)  // Receive FIFO Level Register
#define SC16IS752_IODIR_REG       (0x0A << 3)  // I/O Direction Register
#define SC16IS752_IOSTATE_REG     (0x0B << 3)  // I/O State Register
#define SC16IS752_IOINTENA_REG    (0x0C << 3)  // I/O Interrupt Enable Register
#define SC16IS752_IOCONTROL_REG   (0x0E << 3)  // I/O Control Register
#define SC16IS752_EFCR_REG        (0x0F << 3)  // Extra Features Control Register

// Special register access
#define SC16IS752_DLL_REG         (0x00 << 3)  // Divisor Latch LSB (when LCR[7] = 1)
#define SC16IS752_DLH_REG         (0x01 << 3)  // Divisor Latch MSB (when LCR[7] = 1)
#define SC16IS752_EFR_REG         (0x02 << 3)  // Enhanced Feature Register (when LCR = 0xBF)

// Register bit definitions
#define SC16IS752_LCR_WORD_LEN_8  0x03  // 8 data bits
#define SC16IS752_LCR_STOP_1      0x00  // 1 stop bit
#define SC16IS752_LCR_PARITY_NONE 0x00  // No parity
#define SC16IS752_LCR_DIVISOR_EN  0x80  // Enable divisor latch access
#define SC16IS752_LCR_ENHANCED    0xBF  // Enhanced feature register access

#define SC16IS752_FCR_FIFO_EN     0x01  // Enable FIFO
#define SC16IS752_FCR_RX_RESET    0x02  // Reset RX FIFO
#define SC16IS752_FCR_TX_RESET    0x04  // Reset TX FIFO

#define SC16IS752_IER_RHR         0x01  // Receive Holding Register interrupt
#define SC16IS752_IER_THR         0x02  // Transmit Holding Register interrupt

#define SC16IS752_LSR_DR          0x01  // Data Ready
#define SC16IS752_LSR_THRE        0x20  // Transmit Holding Register Empty
#define SC16IS752_LSR_TEMT        0x40  // Transmitter Empty

#define SC16IS752_EFR_ENABLE      0x10  // Enable enhanced functions

// Baud rate calculation for 115200 with 14.7456 MHz crystal
#define SC16IS752_BAUD_115200_DLL 0x08
#define SC16IS752_BAUD_115200_DLH 0x00

static uint8_t sc16is752_i2c_addr = SC16IS752_DEFAULT_ADDR;

#if CONFIG_IDF_TARGET_ESP32S3 || CONFIG_IDF_TARGET_ESP32H2
static gpio_num_t i2c_gpio_sda = 8;
static gpio_num_t i2c_gpio_scl = 9;
#elif CONFIG_IDF_TARGET_ESP32C3 || CONFIG_IDF_TARGET_ESP32C2
static gpio_num_t i2c_gpio_sda = 5;
static gpio_num_t i2c_gpio_scl = 6;
#else
static gpio_num_t i2c_gpio_sda = 18;
static gpio_num_t i2c_gpio_scl = 19;
#endif

static uint32_t i2c_frequency = 100000;
static i2c_port_t i2c_port = I2C_NUM_0;

static esp_err_t i2c_get_port(int port, i2c_port_t *i2c_port)
{
    if (port >= I2C_NUM_MAX) {
        ESP_LOGE(TAG, "Wrong port number: %d", port);
        return ESP_FAIL;
    }
    *i2c_port = port;
    return ESP_OK;
}

static esp_err_t i2c_master_driver_initialize(void)
{
    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = i2c_gpio_sda,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_io_num = i2c_gpio_scl,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = i2c_frequency,
        // .clk_flags = 0,          /*!< Optional, you can use I2C_SCLK_SRC_FLAG_* flags to choose i2c source clock here. */
    };
    return i2c_param_config(i2c_port, &conf);
}

// SC16IS752 helper functions
static esp_err_t sc16is752_write_register(uint8_t reg_addr, uint8_t data)
{
    esp_err_t ret;
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (sc16is752_i2c_addr << 1) | WRITE_BIT, ACK_CHECK_EN);
    i2c_master_write_byte(cmd, reg_addr, ACK_CHECK_EN);
    i2c_master_write_byte(cmd, data, ACK_CHECK_EN);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(i2c_port, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return ret;
}

static esp_err_t sc16is752_read_register(uint8_t reg_addr, uint8_t *data)
{
    esp_err_t ret;
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (sc16is752_i2c_addr << 1) | WRITE_BIT, ACK_CHECK_EN);
    i2c_master_write_byte(cmd, reg_addr, ACK_CHECK_EN);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (sc16is752_i2c_addr << 1) | READ_BIT, ACK_CHECK_EN);
    i2c_master_read_byte(cmd, data, NACK_VAL);
    i2c_master_stop(cmd);
    ret = i2c_master_cmd_begin(i2c_port, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return ret;
}

static esp_err_t sc16is752_init_uart(void)
{
    esp_err_t ret;

    // Set LCR to enable divisor latch access
    ret = sc16is752_write_register(SC16IS752_LCR_REG, SC16IS752_LCR_DIVISOR_EN);
    if (ret != ESP_OK) return ret;

    // Set baud rate to 115200 (assuming 14.7456 MHz crystal)
    ret = sc16is752_write_register(SC16IS752_DLL_REG, SC16IS752_BAUD_115200_DLL);
    if (ret != ESP_OK) return ret;

    ret = sc16is752_write_register(SC16IS752_DLH_REG, SC16IS752_BAUD_115200_DLH);
    if (ret != ESP_OK) return ret;

    // Access Enhanced Feature Register
    ret = sc16is752_write_register(SC16IS752_LCR_REG, SC16IS752_LCR_ENHANCED);
    if (ret != ESP_OK) return ret;

    // Enable enhanced functions
    ret = sc16is752_write_register(SC16IS752_EFR_REG, SC16IS752_EFR_ENABLE);
    if (ret != ESP_OK) return ret;

    // Set LCR for 8N1 (8 data bits, no parity, 1 stop bit)
    ret = sc16is752_write_register(SC16IS752_LCR_REG,
                                   SC16IS752_LCR_WORD_LEN_8 |
                                   SC16IS752_LCR_STOP_1 |
                                   SC16IS752_LCR_PARITY_NONE);
    if (ret != ESP_OK) return ret;

    // Enable and reset FIFOs
    ret = sc16is752_write_register(SC16IS752_FCR_REG,
                                   SC16IS752_FCR_FIFO_EN |
                                   SC16IS752_FCR_RX_RESET |
                                   SC16IS752_FCR_TX_RESET);
    if (ret != ESP_OK) return ret;

    // Enable receive data ready interrupt
    ret = sc16is752_write_register(SC16IS752_IER_REG, SC16IS752_IER_RHR);
    if (ret != ESP_OK) return ret;

    return ESP_OK;
}

static esp_err_t sc16is752_write_byte(uint8_t data)
{
    esp_err_t ret;
    uint8_t lsr;
    int timeout = 1000; // 1 second timeout

    // Wait for transmit holding register to be empty
    do {
        ret = sc16is752_read_register(SC16IS752_LSR_REG, &lsr);
        if (ret != ESP_OK) return ret;

        if (lsr & SC16IS752_LSR_THRE) {
            break;
        }

        vTaskDelay(1 / portTICK_PERIOD_MS);
        timeout--;
    } while (timeout > 0);

    if (timeout <= 0) {
        return ESP_ERR_TIMEOUT;
    }

    // Write data to transmit holding register
    return sc16is752_write_register(SC16IS752_THR_REG, data);
}

static esp_err_t sc16is752_read_byte(uint8_t *data)
{
    esp_err_t ret;
    uint8_t lsr;

    // Check if data is available
    ret = sc16is752_read_register(SC16IS752_LSR_REG, &lsr);
    if (ret != ESP_OK) return ret;

    if (!(lsr & SC16IS752_LSR_DR)) {
        return ESP_ERR_NOT_FOUND; // No data available
    }

    // Read data from receive holding register
    return sc16is752_read_register(SC16IS752_RHR_REG, data);
}

static int hex_char_to_int(char c)
{
    if (c >= '0' && c <= '9') return c - '0';
    if (c >= 'A' && c <= 'F') return c - 'A' + 10;
    if (c >= 'a' && c <= 'f') return c - 'a' + 10;
    return -1;
}

static char int_to_hex_char(int val)
{
    if (val >= 0 && val <= 9) return '0' + val;
    if (val >= 10 && val <= 15) return 'A' + val - 10;
    return '0';
}

static struct {
    struct arg_int *port;
    struct arg_int *freq;
    struct arg_int *sda;
    struct arg_int *scl;
    struct arg_end *end;
} i2cconfig_args;

static int do_i2cconfig_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&i2cconfig_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, i2cconfig_args.end, argv[0]);
        return 0;
    }

    /* Check "--port" option */
    if (i2cconfig_args.port->count) {
        if (i2c_get_port(i2cconfig_args.port->ival[0], &i2c_port) != ESP_OK) {
            return 1;
        }
    }
    /* Check "--freq" option */
    if (i2cconfig_args.freq->count) {
        i2c_frequency = i2cconfig_args.freq->ival[0];
    }
    /* Check "--sda" option */
    i2c_gpio_sda = i2cconfig_args.sda->ival[0];
    /* Check "--scl" option */
    i2c_gpio_scl = i2cconfig_args.scl->ival[0];
    return 0;
}

static void register_i2cconfig(void)
{
    i2cconfig_args.port = arg_int0(NULL, "port", "<0|1>", "Set the I2C bus port number");
    i2cconfig_args.freq = arg_int0(NULL, "freq", "<Hz>", "Set the frequency(Hz) of I2C bus");
    i2cconfig_args.sda = arg_int1(NULL, "sda", "<gpio>", "Set the gpio for I2C SDA");
    i2cconfig_args.scl = arg_int1(NULL, "scl", "<gpio>", "Set the gpio for I2C SCL");
    i2cconfig_args.end = arg_end(2);
    const esp_console_cmd_t i2cconfig_cmd = {
        .command = "i2cconfig",
        .help = "Config I2C bus",
        .hint = NULL,
        .func = &do_i2cconfig_cmd,
        .argtable = &i2cconfig_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&i2cconfig_cmd));
}

static int do_i2cdetect_cmd(int argc, char **argv)
{
    i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    i2c_master_driver_initialize();
    uint8_t address;
    printf("     0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f\r\n");
    for (int i = 0; i < 128; i += 16) {
        printf("%02x: ", i);
        for (int j = 0; j < 16; j++) {
            fflush(stdout);
            address = i + j;
            i2c_cmd_handle_t cmd = i2c_cmd_link_create();
            i2c_master_start(cmd);
            i2c_master_write_byte(cmd, (address << 1) | WRITE_BIT, ACK_CHECK_EN);
            i2c_master_stop(cmd);
            esp_err_t ret = i2c_master_cmd_begin(i2c_port, cmd, 50 / portTICK_PERIOD_MS);
            i2c_cmd_link_delete(cmd);
            if (ret == ESP_OK) {
                printf("%02x ", address);
            } else if (ret == ESP_ERR_TIMEOUT) {
                printf("UU ");
            } else {
                printf("-- ");
            }
        }
        printf("\r\n");
    }

    i2c_driver_delete(i2c_port);
    return 0;
}

static void register_i2cdetect(void)
{
    const esp_console_cmd_t i2cdetect_cmd = {
        .command = "i2cdetect",
        .help = "Scan I2C bus for devices",
        .hint = NULL,
        .func = &do_i2cdetect_cmd,
        .argtable = NULL
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&i2cdetect_cmd));
}

static struct {
    struct arg_int *chip_address;
    struct arg_int *register_address;
    struct arg_int *data_length;
    struct arg_end *end;
} i2cget_args;

static int do_i2cget_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&i2cget_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, i2cget_args.end, argv[0]);
        return 0;
    }

    /* Check chip address: "-c" option */
    int chip_addr = i2cget_args.chip_address->ival[0];
    /* Check register address: "-r" option */
    int data_addr = -1;
    if (i2cget_args.register_address->count) {
        data_addr = i2cget_args.register_address->ival[0];
    }
    /* Check data length: "-l" option */
    int len = 1;
    if (i2cget_args.data_length->count) {
        len = i2cget_args.data_length->ival[0];
    }
    uint8_t *data = malloc(len);

    i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    i2c_master_driver_initialize();
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    if (data_addr != -1) {
        i2c_master_write_byte(cmd, chip_addr << 1 | WRITE_BIT, ACK_CHECK_EN);
        i2c_master_write_byte(cmd, data_addr, ACK_CHECK_EN);
        i2c_master_start(cmd);
    }
    i2c_master_write_byte(cmd, chip_addr << 1 | READ_BIT, ACK_CHECK_EN);
    if (len > 1) {
        i2c_master_read(cmd, data, len - 1, ACK_VAL);
    }
    i2c_master_read_byte(cmd, data + len - 1, NACK_VAL);
    i2c_master_stop(cmd);
    esp_err_t ret = i2c_master_cmd_begin(i2c_port, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    if (ret == ESP_OK) {
        for (int i = 0; i < len; i++) {
            printf("0x%02x ", data[i]);
            if ((i + 1) % 16 == 0) {
                printf("\r\n");
            }
        }
        if (len % 16) {
            printf("\r\n");
        }
    } else if (ret == ESP_ERR_TIMEOUT) {
        ESP_LOGW(TAG, "Bus is busy");
    } else {
        ESP_LOGW(TAG, "Read failed");
    }
    free(data);
    i2c_driver_delete(i2c_port);
    return 0;
}

static void register_i2cget(void)
{
    i2cget_args.chip_address = arg_int1("c", "chip", "<chip_addr>", "Specify the address of the chip on that bus");
    i2cget_args.register_address = arg_int0("r", "register", "<register_addr>", "Specify the address on that chip to read from");
    i2cget_args.data_length = arg_int0("l", "length", "<length>", "Specify the length to read from that data address");
    i2cget_args.end = arg_end(1);
    const esp_console_cmd_t i2cget_cmd = {
        .command = "i2cget",
        .help = "Read registers visible through the I2C bus",
        .hint = NULL,
        .func = &do_i2cget_cmd,
        .argtable = &i2cget_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&i2cget_cmd));
}

static struct {
    struct arg_int *chip_address;
    struct arg_int *register_address;
    struct arg_int *data;
    struct arg_end *end;
} i2cset_args;

static int do_i2cset_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&i2cset_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, i2cset_args.end, argv[0]);
        return 0;
    }

    /* Check chip address: "-c" option */
    int chip_addr = i2cset_args.chip_address->ival[0];
    /* Check register address: "-r" option */
    int data_addr = 0;
    if (i2cset_args.register_address->count) {
        data_addr = i2cset_args.register_address->ival[0];
    }
    /* Check data: "-d" option */
    int len = i2cset_args.data->count;

    i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    i2c_master_driver_initialize();
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, chip_addr << 1 | WRITE_BIT, ACK_CHECK_EN);
    if (i2cset_args.register_address->count) {
        i2c_master_write_byte(cmd, data_addr, ACK_CHECK_EN);
    }
    for (int i = 0; i < len; i++) {
        i2c_master_write_byte(cmd, i2cset_args.data->ival[i], ACK_CHECK_EN);
    }
    i2c_master_stop(cmd);
    esp_err_t ret = i2c_master_cmd_begin(i2c_port, cmd, 1000 / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Write OK");
    } else if (ret == ESP_ERR_TIMEOUT) {
        ESP_LOGW(TAG, "Bus is busy");
    } else {
        ESP_LOGW(TAG, "Write Failed");
    }
    i2c_driver_delete(i2c_port);
    return 0;
}

static void register_i2cset(void)
{
    i2cset_args.chip_address = arg_int1("c", "chip", "<chip_addr>", "Specify the address of the chip on that bus");
    i2cset_args.register_address = arg_int0("r", "register", "<register_addr>", "Specify the address on that chip to read from");
    i2cset_args.data = arg_intn(NULL, NULL, "<data>", 0, 256, "Specify the data to write to that data address");
    i2cset_args.end = arg_end(2);
    const esp_console_cmd_t i2cset_cmd = {
        .command = "i2cset",
        .help = "Set registers visible through the I2C bus",
        .hint = NULL,
        .func = &do_i2cset_cmd,
        .argtable = &i2cset_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&i2cset_cmd));
}

static struct {
    struct arg_int *chip_address;
    struct arg_int *size;
    struct arg_end *end;
} i2cdump_args;

static int do_i2cdump_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&i2cdump_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, i2cdump_args.end, argv[0]);
        return 0;
    }

    /* Check chip address: "-c" option */
    int chip_addr = i2cdump_args.chip_address->ival[0];
    /* Check read size: "-s" option */
    int size = 1;
    if (i2cdump_args.size->count) {
        size = i2cdump_args.size->ival[0];
    }
    if (size != 1 && size != 2 && size != 4) {
        ESP_LOGE(TAG, "Wrong read size. Only support 1,2,4");
        return 1;
    }
    i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    i2c_master_driver_initialize();
    uint8_t data_addr;
    uint8_t data[4];
    int32_t block[16];
    printf("     0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f"
           "    0123456789abcdef\r\n");
    for (int i = 0; i < 128; i += 16) {
        printf("%02x: ", i);
        for (int j = 0; j < 16; j += size) {
            fflush(stdout);
            data_addr = i + j;
            i2c_cmd_handle_t cmd = i2c_cmd_link_create();
            i2c_master_start(cmd);
            i2c_master_write_byte(cmd, chip_addr << 1 | WRITE_BIT, ACK_CHECK_EN);
            i2c_master_write_byte(cmd, data_addr, ACK_CHECK_EN);
            i2c_master_start(cmd);
            i2c_master_write_byte(cmd, chip_addr << 1 | READ_BIT, ACK_CHECK_EN);
            if (size > 1) {
                i2c_master_read(cmd, data, size - 1, ACK_VAL);
            }
            i2c_master_read_byte(cmd, data + size - 1, NACK_VAL);
            i2c_master_stop(cmd);
            esp_err_t ret = i2c_master_cmd_begin(i2c_port, cmd, 50 / portTICK_PERIOD_MS);
            i2c_cmd_link_delete(cmd);
            if (ret == ESP_OK) {
                for (int k = 0; k < size; k++) {
                    printf("%02x ", data[k]);
                    block[j + k] = data[k];
                }
            } else {
                for (int k = 0; k < size; k++) {
                    printf("XX ");
                    block[j + k] = -1;
                }
            }
        }
        printf("   ");
        for (int k = 0; k < 16; k++) {
            if (block[k] < 0) {
                printf("X");
            }
            if ((block[k] & 0xff) == 0x00 || (block[k] & 0xff) == 0xff) {
                printf(".");
            } else if ((block[k] & 0xff) < 32 || (block[k] & 0xff) >= 127) {
                printf("?");
            } else {
                printf("%c", (char)(block[k] & 0xff));
            }
        }
        printf("\r\n");
    }
    i2c_driver_delete(i2c_port);
    return 0;
}

static void register_i2cdump(void)
{
    i2cdump_args.chip_address = arg_int1("c", "chip", "<chip_addr>", "Specify the address of the chip on that bus");
    i2cdump_args.size = arg_int0("s", "size", "<size>", "Specify the size of each read");
    i2cdump_args.end = arg_end(1);
    const esp_console_cmd_t i2cdump_cmd = {
        .command = "i2cdump",
        .help = "Examine registers visible through the I2C bus",
        .hint = NULL,
        .func = &do_i2cdump_cmd,
        .argtable = &i2cdump_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&i2cdump_cmd));
}

// SC16IS752 command structures and functions
static struct {
    struct arg_int *address;
    struct arg_end *end;
} sc16is752_init_args;

static int do_sc16is752_init_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&sc16is752_init_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, sc16is752_init_args.end, argv[0]);
        return 0;
    }

    // Check address option
    if (sc16is752_init_args.address->count) {
        sc16is752_i2c_addr = sc16is752_init_args.address->ival[0];
    }

    printf("Initializing SC16IS752 at I2C address 0x%02X\n", sc16is752_i2c_addr);

    // Initialize I2C driver
    esp_err_t ret = i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install I2C driver");
        return 1;
    }

    ret = i2c_master_driver_initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize I2C master");
        i2c_driver_delete(i2c_port);
        return 1;
    }

    // Initialize SC16IS752 UART
    ret = sc16is752_init_uart();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SC16IS752 UART: %s", esp_err_to_name(ret));
        i2c_driver_delete(i2c_port);
        return 1;
    }

    printf("SC16IS752 initialized successfully for 115200 baud\n");
    i2c_driver_delete(i2c_port);
    return 0;
}

static struct {
    struct arg_str *data;
    struct arg_end *end;
} sc16is752_send_args;

static int do_sc16is752_send_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&sc16is752_send_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, sc16is752_send_args.end, argv[0]);
        return 0;
    }

    const char *hex_string = sc16is752_send_args.data->sval[0];
    int len = strlen(hex_string);

    // Validate hex string length (must be even)
    if (len % 2 != 0) {
        ESP_LOGE(TAG, "Hex string must have even number of characters");
        return 1;
    }

    printf("Sending hex string: %s\n", hex_string);

    // Initialize I2C driver
    esp_err_t ret = i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install I2C driver");
        return 1;
    }

    ret = i2c_master_driver_initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize I2C master");
        i2c_driver_delete(i2c_port);
        return 1;
    }

    // Send hex string as bytes
    for (int i = 0; i < len; i += 2) {
        int high_nibble = hex_char_to_int(hex_string[i]);
        int low_nibble = hex_char_to_int(hex_string[i + 1]);

        if (high_nibble < 0 || low_nibble < 0) {
            ESP_LOGE(TAG, "Invalid hex character at position %d", i);
            i2c_driver_delete(i2c_port);
            return 1;
        }

        uint8_t byte_val = (high_nibble << 4) | low_nibble;
        ret = sc16is752_write_byte(byte_val);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to send byte 0x%02X: %s", byte_val, esp_err_to_name(ret));
            i2c_driver_delete(i2c_port);
            return 1;
        }

        printf("Sent: 0x%02X\n", byte_val);
        vTaskDelay(10 / portTICK_PERIOD_MS); // Small delay between bytes
    }

    printf("Hex string sent successfully\n");
    i2c_driver_delete(i2c_port);
    return 0;
}

static struct {
    struct arg_int *timeout;
    struct arg_int *max_bytes;
    struct arg_end *end;
} sc16is752_receive_args;

static int do_sc16is752_receive_cmd(int argc, char **argv)
{
    int nerrors = arg_parse(argc, argv, (void **)&sc16is752_receive_args);
    if (nerrors != 0) {
        arg_print_errors(stderr, sc16is752_receive_args.end, argv[0]);
        return 0;
    }

    int timeout_ms = 5000; // Default 5 seconds
    int max_bytes = 64;    // Default max 64 bytes

    if (sc16is752_receive_args.timeout->count) {
        timeout_ms = sc16is752_receive_args.timeout->ival[0];
    }

    if (sc16is752_receive_args.max_bytes->count) {
        max_bytes = sc16is752_receive_args.max_bytes->ival[0];
    }

    printf("Waiting for data (timeout: %d ms, max bytes: %d)...\n", timeout_ms, max_bytes);

    // Initialize I2C driver
    esp_err_t ret = i2c_driver_install(i2c_port, I2C_MODE_MASTER, I2C_MASTER_RX_BUF_DISABLE, I2C_MASTER_TX_BUF_DISABLE, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install I2C driver");
        return 1;
    }

    ret = i2c_master_driver_initialize();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize I2C master");
        i2c_driver_delete(i2c_port);
        return 1;
    }

    uint8_t received_data[256];
    int bytes_received = 0;
    int timeout_count = timeout_ms / 10; // Check every 10ms

    while (timeout_count > 0 && bytes_received < max_bytes) {
        uint8_t data;
        ret = sc16is752_read_byte(&data);

        if (ret == ESP_OK) {
            received_data[bytes_received++] = data;
            printf("Received: 0x%02X\n", data);
            timeout_count = timeout_ms / 10; // Reset timeout on successful read
        } else if (ret == ESP_ERR_NOT_FOUND) {
            // No data available, continue waiting
            vTaskDelay(10 / portTICK_PERIOD_MS);
            timeout_count--;
        } else {
            ESP_LOGE(TAG, "Error reading data: %s", esp_err_to_name(ret));
            break;
        }
    }

    if (bytes_received > 0) {
        printf("\nReceived %d bytes as hex string: ", bytes_received);
        for (int i = 0; i < bytes_received; i++) {
            printf("%02X", received_data[i]);
        }
        printf("\n");
    } else {
        printf("No data received within timeout period\n");
    }

    i2c_driver_delete(i2c_port);
    return 0;
}

void register_sc16is752(void)
{
    // Register sc16is752init command
    sc16is752_init_args.address = arg_int0("a", "addr", "<address>", "SC16IS752 I2C address (default: 0x4D)");
    sc16is752_init_args.end = arg_end(1);
    const esp_console_cmd_t sc16is752_init_cmd = {
        .command = "sc16is752init",
        .help = "Initialize SC16IS752 I2C to Serial converter for 115200 baud",
        .hint = NULL,
        .func = &do_sc16is752_init_cmd,
        .argtable = &sc16is752_init_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&sc16is752_init_cmd));

    // Register sc16is752send command
    sc16is752_send_args.data = arg_str1(NULL, NULL, "<hex_string>", "Hex string to send (e.g., 'DEADBEEF')");
    sc16is752_send_args.end = arg_end(1);
    const esp_console_cmd_t sc16is752_send_cmd = {
        .command = "sc16is752send",
        .help = "Send hex string through SC16IS752 serial port",
        .hint = NULL,
        .func = &do_sc16is752_send_cmd,
        .argtable = &sc16is752_send_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&sc16is752_send_cmd));

    // Register sc16is752receive command
    sc16is752_receive_args.timeout = arg_int0("t", "timeout", "<ms>", "Timeout in milliseconds (default: 5000)");
    sc16is752_receive_args.max_bytes = arg_int0("m", "max", "<bytes>", "Maximum bytes to receive (default: 64)");
    sc16is752_receive_args.end = arg_end(2);
    const esp_console_cmd_t sc16is752_receive_cmd = {
        .command = "sc16is752receive",
        .help = "Receive data from SC16IS752 serial port and display as hex",
        .hint = NULL,
        .func = &do_sc16is752_receive_cmd,
        .argtable = &sc16is752_receive_args
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&sc16is752_receive_cmd));
}

void register_i2ctools(void)
{
    register_i2cconfig();
    register_i2cdetect();
    register_i2cget();
    register_i2cset();
    register_i2cdump();
    register_sc16is752();
}
